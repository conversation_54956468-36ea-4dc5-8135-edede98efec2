import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.preprocessing import LabelEncoder
from sklearn.calibration import CalibratedClassifierCV
import warnings
import time

# 忽略一些未来的警告，保持输出整洁
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)


# --- 1. 数据加载与预处理 (无变化) ---
def preprocess_data(filepath='湮灭统计历史.csv'):
    try:
        df = pd.read_csv(filepath, encoding='utf-8')
    except FileNotFoundError:
        print(f"错误：未找到文件 '{filepath}'。请确保CSV文件与脚本在同一目录下。")
        return None, None
    locations = ['炼体殿', '淬骨堂', '锻魂阁', '凝神台', '破境塔']
    location_cols = [f'{loc}_次' for loc in locations]
    annihilated_sequence = []
    for _, row in df.iterrows():
        found = False
        for col, loc_name in zip(location_cols, locations):
            if isinstance(row[col], str) and '*' in row[col]:
                annihilated_sequence.append(loc_name)
                found = True
                break
        if not found: annihilated_sequence.append(None)
    annihilated_sequence = [loc for loc in annihilated_sequence if loc is not None]
    print(f"数据加载完成。共处理了 {len(annihilated_sequence)} 条有效湮灭记录。")
    return annihilated_sequence, locations


# --- 2. 特征工程 (无变化) ---
def create_features_and_labels(sequence, locations, n_features=4):
    encoder = LabelEncoder().fit(locations)
    encoded_sequence = encoder.transform(sequence)
    X, y = [], []
    for i in range(len(encoded_sequence) - n_features):
        X.append(encoded_sequence[i: i + n_features])
        y.append(encoded_sequence[i + n_features])
    print(f"特征工程完成。生成了 {len(X)} 个样本，每个样本包含 {n_features} 个历史特征。")
    return np.array(X), np.array(y), encoder


# --- 3. 核心：模块化的回测函数 (已优化，不再打印实时日志以加速) ---
def run_backtest_for_single_run(X, y, encoder,
                                window_size, test_size, bet_confidence_threshold):
    """
    一个精简的回测函数，只在最后返回结果，用于快速的网格搜索。
    """
    balance = 0.0
    bets_placed = 0
    win_count = 0
    WIN_PAYOUT = 0.23
    LOSS_STAKE = -1.0

    start_point = len(X) - test_size

    for i in range(start_point, len(X)):
        train_start_index = max(0, i - window_size)
        X_train, y_train = X[train_start_index:i], y[train_start_index:i]
        X_test_sample = X[i].reshape(1, -1)
        actual_loser_encoded = y[i]

        base_lgbm = lgb.LGBMClassifier(random_state=42, n_jobs=-1, verbosity=-1)
        calibrated_model = CalibratedClassifierCV(base_lgbm, method='isotonic', cv=3)
        calibrated_model.fit(X_train, y_train)
        
        predicted_probs = calibrated_model.predict_proba(X_test_sample)[0]
        lowest_prob = np.min(predicted_probs)
        
        if lowest_prob < bet_confidence_threshold:
            bets_placed += 1
            our_bet_encoded = np.argmin(predicted_probs)
            if our_bet_encoded != actual_loser_encoded:
                win_count += 1
                balance += WIN_PAYOUT
            else:
                balance += LOSS_STAKE
    
    # 返回一个包含所有关键指标的字典
    if bets_placed > 0:
        win_rate = win_count / bets_placed
    else:
        win_rate = 0
        
    return {
        "final_balance": balance,
        "win_rate": win_rate,
        "bets_placed": bets_placed
    }


# --- 4. 主程序：定义参数网格并运行优化器 ---
if __name__ == '__main__':
    # =================================================================
    # ---              【自动化策略优化器】                           ---
    # =================================================================
    
    # 步骤1: 定义固定的核心参数
    TEST_SIZE = 500
    N_FEATURES = 4
    
    # 步骤2: 定义您允许程序探索的参数网格
    param_grid = {
        'training_window': [2500, 3000, 3500],
        'bet_threshold': [0.15, 0.16, 0.165]
    }
    
    # =================================================================
    
    sequence, locations = preprocess_data()
    
    if sequence and locations and len(sequence) >= (TEST_SIZE + max(param_grid['training_window'])):
        X, y, encoder = create_features_and_labels(sequence, locations, n_features=N_FEATURES)
        
        results = []
        best_balance = -np.inf
        best_params = {}
        
        total_runs = len(param_grid['training_window']) * len(param_grid['bet_threshold'])
        current_run = 0

        print(f"\n--- 开始执行网格搜索优化，总共需要进行 {total_runs} 次完整回测 ---")

        # 步骤3: 遍历所有参数组合
        for window in param_grid['training_window']:
            for threshold in param_grid['bet_threshold']:
                current_run += 1
                start_time = time.time()
                
                print(f"\n[{current_run}/{total_runs}] 正在测试组合: 训练窗口={window}, 投注门槛={threshold:.3f}")
                
                # 为当前组合运行一次回测
                result_dict = run_backtest_for_single_run(
                    X, y, encoder,
                    window_size=window,
                    test_size=TEST_SIZE,
                    bet_confidence_threshold=threshold
                )
                
                # 记录结果
                params = {'window': window, 'threshold': threshold}
                results.append({**params, **result_dict})
                
                end_time = time.time()
                print(f"  -> 测试完成。耗时: {end_time - start_time:.2f}秒. "
                      f"最终收益: {result_dict['final_balance']:.2f}, "
                      f"胜率: {result_dict['win_rate']:.2%}, "
                      f"下注次数: {result_dict['bets_placed']}")

                # 更新最优结果
                if result_dict['final_balance'] > best_balance:
                    best_balance = result_dict['final_balance']
                    best_params = params

        # 步骤4: 打印最终的优化报告
        print("\n" + "="*30 + "\n   所有参数组合测试结果\n" + "="*30)
        
        # 将结果转换为DataFrame以便清晰展示
        results_df = pd.DataFrame(results)
        # 格式化输出
        results_df['final_balance'] = results_df['final_balance'].map('{:,.2f}'.format)
        results_df['win_rate'] = results_df['win_rate'].map('{:.2%}'.format)
        
        print(results_df.to_string())

        print("\n" + "="*30 + "\n   最优参数组合\n" + "="*30)
        print(f"经过 {total_runs} 次测试，最优的参数组合是:")
        print(f"  - 训练窗口 (training_window): {best_params['window']}")
        print(f"  - 投注门槛 (bet_threshold): {best_params['threshold']}")
        print(f"该组合在 {TEST_SIZE} 次测试中，取得了最高的最终收益: {best_balance:.2f} 个单位")

    else:
        print("错误：数据量不足，无法进行设定的滑动窗口回测。")