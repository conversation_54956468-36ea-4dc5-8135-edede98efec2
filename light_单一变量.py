import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.preprocessing import LabelEncoder
from sklearn.calibration import CalibratedClassifierCV
import warnings

# 忽略一些未来的警告，保持输出整洁
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)


# --- 1. 数据加载与预处理 (无变化) ---
def preprocess_data(filepath='湮灭统计历史.csv'):
    try:
        df = pd.read_csv(filepath, encoding='utf-8')
    except FileNotFoundError:
        print(f"错误：未找到文件 '{filepath}'。请确保CSV文件与脚本在同一目录下。")
        return None, None
    locations = ['炼体殿', '淬骨堂', '锻魂阁', '凝神台', '破境塔']
    location_cols = [f'{loc}_次' for loc in locations]
    annihilated_sequence = []
    for _, row in df.iterrows():
        found = False
        for col, loc_name in zip(location_cols, locations):
            if isinstance(row[col], str) and '*' in row[col]:
                annihilated_sequence.append(loc_name)
                found = True
                break
        if not found: annihilated_sequence.append(None)
    annihilated_sequence = [loc for loc in annihilated_sequence if loc is not None]
    print(f"数据加载完成。共处理了 {len(annihilated_sequence)} 条有效湮灭记录。")
    return annihilated_sequence, locations


# --- 2. 特征工程 (无变化) ---
def create_features_and_labels(sequence, locations, n_features=4):
    encoder = LabelEncoder().fit(locations)
    encoded_sequence = encoder.transform(sequence)
    X, y = [], []
    for i in range(len(encoded_sequence) - n_features):
        X.append(encoded_sequence[i: i + n_features])
        y.append(encoded_sequence[i + n_features])
    print(f"特征工程完成。生成了 {len(X)} 个样本，每个样本包含 {n_features} 个历史特征。")
    return np.array(X), np.array(y), encoder


# --- 3. 核心：LightGBM实时回测函数 (已优化) ---
def run_lightgbm_backtest(X, y, encoder, locations,
                           window_size, test_size, bet_confidence_threshold):
    """
    专门为LightGBM设计的、实时打印结果的滑动窗口回测函数。
    """
    balance = 0.0
    bets_placed = 0
    win_count = 0
    WIN_PAYOUT = 0.23
    LOSS_STAKE = -1.0

    print("\n--- 开始执行 LightGBM 动态实时回测 ---")
    print(f"策略: 校准的 LightGBM + 滑动窗口 + 置信度投注")
    print(f"训练窗口: {window_size}, 测试集大小: {test_size}, 投注门槛: <{bet_confidence_threshold:.1%}")
    print("-" * 60)

    start_point = len(X) - test_size
    total_predictions = test_size

    for i in range(start_point, len(X)):
        train_start_index = max(0, i - window_size)
        X_train, y_train = X[train_start_index:i], y[train_start_index:i]
        X_test_sample = X[i].reshape(1, -1)
        actual_loser_encoded = y[i]

        # 创建基础LightGBM模型
        base_lgbm = lgb.LGBMClassifier(random_state=42, n_jobs=-1, verbosity=-1)
        
        # 使用概率校准包装模型
        calibrated_model = CalibratedClassifierCV(base_lgbm, method='isotonic', cv=3)
        calibrated_model.fit(X_train, y_train)
        
        # 预测校准后的概率
        predicted_probs = calibrated_model.predict_proba(X_test_sample)[0]
        lowest_prob = np.min(predicted_probs)
        
        progress = i - start_point + 1
        
        # 【核心改动】实时打印每一次的决策和结果
        decision_log = f"进度: {progress}/{total_predictions}"
        
        if lowest_prob < bet_confidence_threshold:
            bets_placed += 1
            our_bet_encoded = np.argmin(predicted_probs)
            our_bet_name = encoder.inverse_transform([our_bet_encoded])[0]
            actual_loser_name = encoder.inverse_transform([actual_loser_encoded])[0]
            
            if our_bet_encoded == actual_loser_encoded:
                balance += LOSS_STAKE
                result = "失败"
            else:
                balance += WIN_PAYOUT
                win_count += 1
                result = "成功"
            
            decision_log += f" | 【下注】 | 预测最安全: {our_bet_name:<4} (概率 {lowest_prob:.2%}) | 实际: {actual_loser_name:<4} | 结果: {result} | 收益: {balance:7.2f}"
        else:
            decision_log += f" | 【放弃】 | 最低概率 {lowest_prob:.2%} 未达门槛。"

        print(decision_log)

    # 打印最终的总结报告
    print("\n" + "="*25 + "\n   最终回测总结\n" + "="*25)
    if bets_placed > 0:
        win_rate = win_count / bets_placed
        print(f"总共考察了 {total_predictions} 次机会。")
        print(f"下注次数: {bets_placed} (频率: {bets_placed/total_predictions:.2%})")
        print(f"下注后胜率: {win_rate:.2%}")
        print(f"成功: {win_count}, 失败: {bets_placed - win_count}")
        print(f"最终总收益: {balance:.2f} 个单位")
        print(f"期望收益 (E.V.)/每次下注: {balance/bets_placed:.4f} 个单位")
    else:
        print("在整个回测期间，没有任何一次机会满足下注的置信度门槛。")


# --- 4. 主程序：定义参数并运行 ---
if __name__ == '__main__':
    # =================================================================
    # ---                  【核心可调参数】                          ---
    # =================================================================
    TRAINING_WINDOW_SIZE = 3500  # 动态训练窗口大小
    TEST_SIZE =  500          # 使用最后1000条数据进行测试
    
    # 按照您的要求，精确设置置信度门槛为 0.15
    BET_CONFIDENCE_THRESHOLD = 0.16 
    # =================================================================
    
    # 步骤1: 加载和预处理数据
    sequence, locations = preprocess_data()
    
    if sequence and locations and len(sequence) >= (TEST_SIZE + 500):
        X, y, encoder = create_features_and_labels(sequence, locations, n_features=4)
        
        # 步骤2: 运行专门的LightGBM回测
        run_lightgbm_backtest(
            X, y, encoder, locations,
            window_size=TRAINING_WINDOW_SIZE,
            test_size=TEST_SIZE,
            bet_confidence_threshold=BET_CONFIDENCE_THRESHOLD
        )
    else:
        print("错误：数据量不足，无法进行设定的滑动窗口回测。")