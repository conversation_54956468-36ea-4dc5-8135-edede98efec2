import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import LabelEncoder
from sklearn.calibration import CalibratedClassifierCV
from sklearn.base import clone  # 用于克隆模型实例
import lightgbm as lgb
import xgboost as xgb
import warnings

# 忽略一些未来的警告，保持输出整洁
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)


# --- 1. 数据加载与预处理 (无变化) ---
def preprocess_data(filepath='湮灭统计历史.csv'):
    try:
        df = pd.read_csv(filepath, encoding='utf-8')
    except FileNotFoundError:
        print(f"错误：未找到文件 '{filepath}'。请确保CSV文件与脚本在同一目录下。")
        return None, None
    locations = ['炼体殿', '淬骨堂', '锻魂阁', '凝神台', '破境塔']
    location_cols = [f'{loc}_次' for loc in locations]
    annihilated_sequence = []
    for _, row in df.iterrows():
        found = False
        for col, loc_name in zip(location_cols, locations):
            if isinstance(row[col], str) and '*' in row[col]:
                annihilated_sequence.append(loc_name)
                found = True
                break
        if not found: annihilated_sequence.append(None)
    annihilated_sequence = [loc for loc in annihilated_sequence if loc is not None]
    print(f"数据加载完成。共处理了 {len(annihilated_sequence)} 条有效湮灭记录。")
    return annihilated_sequence, locations


# --- 2. 特征工程 (无变化) ---
def create_features_and_labels(sequence, locations, n_features=4):
    encoder = LabelEncoder().fit(locations)
    encoded_sequence = encoder.transform(sequence)
    X, y = [], []
    for i in range(len(encoded_sequence) - n_features):
        X.append(encoded_sequence[i: i + n_features])
        y.append(encoded_sequence[i + n_features])
    print(f"特征工程完成。生成了 {len(X)} 个样本，每个样本包含 {n_features} 个历史特征。")
    return np.array(X), np.array(y), encoder


# --- 3. 统一的回测函数 ---
def run_backtest_for_model(X, y, encoder, model_name, model_instance,
                           window_size, test_size, bet_confidence_threshold):
    """
    一个通用的回测函数，可以为任何传入的模型执行滑动窗口回测。
    """
    balance = 0.0
    bets_placed = 0
    win_count = 0
    WIN_PAYOUT = 0.23
    LOSS_STAKE = -1.0

    print(f"策略: 校准的 {model_name} + 滑动窗口 + 置信度投注")
    print(f"训练窗口: {window_size}, 测试集大小: {test_size}, 投注门槛: <{bet_confidence_threshold:.1%}")
    print("-" * 60)

    start_point = len(X) - test_size
    total_predictions = test_size

    for i in range(start_point, len(X)):
        train_start_index = max(0, i - window_size)
        X_train, y_train = X[train_start_index:i], y[train_start_index:i]
        X_test_sample = X[i].reshape(1, -1)
        actual_loser_encoded = y[i]

        # 为保证公平，每次循环都创建一个全新的、未训练过的模型实例
        current_model = clone(model_instance)
        
        # 对当前模型进行概率校准
        calibrated_model = CalibratedClassifierCV(current_model, method='isotonic', cv=3)
        calibrated_model.fit(X_train, y_train)
        
        predicted_probs = calibrated_model.predict_proba(X_test_sample)[0]
        lowest_prob = np.min(predicted_probs)
        
        progress = i - start_point + 1
        
        # 决策逻辑
        if lowest_prob < bet_confidence_threshold:
            bets_placed += 1
            our_bet_encoded = np.argmin(predicted_probs)
            
            if our_bet_encoded == actual_loser_encoded:
                balance += LOSS_STAKE
            else:
                balance += WIN_PAYOUT
                win_count += 1
        
        # 为了避免刷屏，可以只在特定间隔打印进度
        if progress % 100 == 0 or progress == total_predictions:
            print(f"  ...进度: {progress}/{total_predictions}, 当前收益: {balance:.2f}")

    # 打印最终的总结报告
    print("\n--- 回测总结 ---")
    if bets_placed > 0:
        win_rate = win_count / bets_placed
        print(f"总共考察了 {total_predictions} 次机会。")
        print(f"下注次数: {bets_placed} (频率: {bets_placed/total_predictions:.2%})")
        print(f"下注后胜率: {win_rate:.2%}")
        print(f"成功: {win_count}, 失败: {bets_placed - win_count}")
        print(f"最终总收益: {balance:.2f} 个单位")
        print(f"期望收益 (E.V.)/每次下注: {balance/bets_placed:.4f} 个单位")
    else:
        print("在整个回测期间，没有任何一次机会满足下注的置信度门槛。")


# --- 4. 主程序：定义并运行所有模型的对比测试 ---
if __name__ == '__main__':
    # =================================================================
    # ---                  【核心可调参数】                          ---
    # =================================================================
    TRAINING_WINDOW_SIZE = 1500  # 动态训练窗口大小
    TEST_SIZE = 500           # 使用最后1000条数据进行测试
    BET_CONFIDENCE_THRESHOLD = 0.17 # 统一的投注门槛
    # =================================================================
    
    # 步骤1: 加载和预处理数据
    sequence, locations = preprocess_data()
    
    if sequence and locations and len(sequence) >= (TEST_SIZE + TRAINING_WINDOW_SIZE):
        X, y, encoder = create_features_and_labels(sequence, locations, n_features=4)
        
        # 步骤2: 定义所有要参加比赛的选手（模型）
        models_to_test = {
            "Random Forest": RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1),
            "LightGBM": lgb.LGBMClassifier(random_state=42, n_jobs=-1),
            "XGBoost": xgb.XGBClassifier(random_state=42, n_jobs=-1)
        }
        
        # 步骤3: 依次为每个模型举办擂台赛
        for model_name, model_instance in models_to_test.items():
            print(f"\n{'='*25}\n   开始测试模型: {model_name}\n{'='*25}")
            run_backtest_for_model(
                X, y, encoder,
                model_name, model_instance,
                window_size=TRAINING_WINDOW_SIZE,
                test_size=TEST_SIZE,
                bet_confidence_threshold=BET_CONFIDENCE_THRESHOLD
            )
            print(f"{'='*25}\n   模型 {model_name} 测试结束\n{'='*25}")

    else:
        print("错误：数据量不足，无法进行设定的滑动窗口回测。")